TITLE: tview Application and Box Input Capture API
DESCRIPTION: API documentation for the `SetInputCapture` methods available on `tview.Application` and `tview.Box` types. These methods allow developers to register callback functions that are invoked upon key press events, enabling custom input handling.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_2

LANGUAGE: APIDOC
CODE:
```
Application.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey)
  - Description: The provided callback function is invoked when a key is pressed. Whatever key event you return will be passed on to the default Application handling.
Box.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey)
  - Description: Same as Application.SetInputCapture() but on a primitive level. The callback function is invoked when the primitive has focus and a key is pressed.
```

----------------------------------------

TITLE: Create and display a basic tview Box in Go
DESCRIPTION: This Go example demonstrates how to initialize a `tview.Box` with a border and title, then display it using `tview.NewApplication().SetRoot()`. It sets up a minimal terminal UI application to render the box.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Box.md#_snippet_0

LANGUAGE: Go
CODE:
```
package main

import "github.com/rivo/tview"

func main() {
	box := tview.NewBox().
		SetBorder(true).
		SetTitle("Box Demo")
	if err := tview.NewApplication().SetRoot(box, true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Create a Basic tview InputField in Go
DESCRIPTION: This Go example demonstrates how to initialize a simple `tview.InputField` that accepts only integer input. It sets a label, defines the field width, and configures a done function to stop the application upon completion. The example utilizes `tcell` for handling key events.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/InputField.md#_snippet_0

LANGUAGE: Go
CODE:
```
package main

import (
	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

func main() {
	app := tview.NewApplication()
	inputField := tview.NewInputField().
		SetLabel("Enter a number: ").
		SetFieldWidth(10).
		SetAcceptanceFunc(tview.InputFieldInteger).
		SetDoneFunc(func(key tcell.Key) {
			app.Stop()
		})
	if err := app.SetRoot(inputField, true).SetFocus(inputField).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Draw Horizontal Line and Text in tview.TextView using SetDrawFunc
DESCRIPTION: This Go example illustrates applying `SetDrawFunc` to a `tview.TextView` to add a horizontal line and centered text. Similar to the `Box` example, it manipulates the `tcell.Screen` directly, showcasing how to customize drawing within a `TextView` while preserving space for its original content.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_1

LANGUAGE: Go
CODE:
```
tview.NewTextView().
  SetText("This is the text view's content").
  SetTextAlign(tview.AlignCenter)
textView.SetBorder(true).
  SetDrawFunc(func(screen tcell.Screen, x int, y int, width int, height int) (int, int, int, int) {
    // Draw a horizontal line across the middle of the box.
    centerY := y + height/2
    for cx := x + 1; cx < x+width-1; cx++ {
      screen.SetContent(cx, centerY, tview.GraphicsHoriBar, nil, tcell.StyleDefault.Foreground(tcell.ColorWhite))
    }

    // Write som text along the horizontal line.
    tview.Print(screen, " Center Line ", x+1, centerY, width-2, tview.AlignCenter, tcell.ColorYellow)

    // Space for other content.
    return x + 1, centerY + 1, width - 2, height - (centerY + 1 - y)
  })
```

----------------------------------------

TITLE: Create a basic tview Button in Go
DESCRIPTION: This Go snippet demonstrates how to create a simple button using the `tview` library. It initializes a new application, creates a button with a custom function to stop the app on selection, sets a border, and defines its rectangular dimensions. The application then runs, displaying the button.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Button.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import "github.com/rivo/tview"

func main() {
	app := tview.NewApplication()
	button := tview.NewButton("Hit Enter to close").SetSelectedFunc(func() {
		app.Stop()
	})
	button.SetBorder(true).SetRect(0, 0, 22, 3)
	if err := app.SetRoot(button, false).SetFocus(button).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Display 'Hello, World!' box in terminal with tview
DESCRIPTION: A basic Go program demonstrating how to create a simple bordered box with a title and display it using the tview application wrapper. This serves as a minimal example to get started with tview.
SOURCE: https://github.com/rivo/tview/blob/master/README.md#_snippet_1

LANGUAGE: go
CODE:
```
package main

import (
	"github.com/rivo/tview"
)

func main() {
	box := tview.NewBox().SetBorder(true).SetTitle("Hello, world!")
	if err := tview.NewApplication().SetRoot(box, true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Draw Horizontal Line and Text in tview.Box using SetDrawFunc
DESCRIPTION: This Go example demonstrates how to use `SetDrawFunc` on a `tview.Box` to draw a horizontal line and centered text. It accesses the `tcell.Screen` directly to customize the box's appearance after its initial drawing, returning coordinates for subsequent content.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_0

LANGUAGE: Go
CODE:
```
tview.NewBox().
  SetBorder(true).
  SetDrawFunc(func(screen tcell.Screen, x int, y int, width int, height int) (int, int, int, int) {
    // Draw a horizontal line across the middle of the box.
    centerY := y + height/2
    for cx := x + 1; cx < x+width-1; cx++ {
      screen.SetContent(cx, centerY, tview.BoxDrawingsLightHorizontal, nil, tcell.StyleDefault.Foreground(tcell.ColorWhite))
    }

    // Write some text along the horizontal line.
    tview.Print(screen, " Center Line ", x+1, centerY, width-2, tview.AlignCenter, tcell.ColorYellow)

    // Space for other content.
    return x + 1, centerY + 1, width - 2, height - (centerY + 1 - y)
  })
```

----------------------------------------

TITLE: Create Selectable List with Tview in Go
DESCRIPTION: This Go snippet demonstrates how to initialize a Tview application and create a `List` component. It adds several selectable items, each with a description and a keyboard shortcut. The 'Quit' item includes a function to stop the application. The list is then set as the root and focused, handling potential errors during execution.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/List.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import (
	"github.com/rivo/tview"
)

func main() {
	app := tview.NewApplication()
	list := tview.NewList().
		AddItem("List item 1", "Some explanatory text", 'a', nil).
		AddItem("List item 2", "Some explanatory text", 'b', nil).
		AddItem("List item 3", "Some explanatory text", 'c', nil).
		AddItem("List item 4", "Some explanatory text", 'd', nil).
		AddItem("Quit", "Press to exit", 'q', func() {
			app.Stop()
		})
	if err := app.SetRoot(list, true).SetFocus(list).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Go Tview File System Explorer Example
DESCRIPTION: This Go program demonstrates how to build a simple file system explorer using the `tview` library. It creates a navigable tree view that displays directories and files, allowing users to expand directories to view their contents and collapse them. It utilizes `tview.NewTreeView`, `tview.NewTreeNode`, and `ioutil.ReadDir` to populate the tree dynamically.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/TreeView.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import (
	"io/ioutil"
	"path/filepath"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// Show a navigable tree view of the current directory.
func main() {
	rootDir := "."
	root := tview.NewTreeNode(rootDir).
		SetColor(tcell.ColorRed)
	tree := tview.NewTreeView().
		SetRoot(root).
		SetCurrentNode(root)

	// A helper function which adds the files and directories of the given path
	// to the given target node.
	add := func(target *tview.TreeNode, path string) {
		files, err := ioutil.ReadDir(path)
		if err != nil {
			panic(err)
		}
		for _, file := range files {
			node := tview.NewTreeNode(file.Name()).
				SetReference(filepath.Join(path, file.Name())).
				SetSelectable(file.IsDir())
			if file.IsDir() {
				node.SetColor(tcell.ColorGreen)
			}
			target.AddChild(node)
		}
	}

	// Add the current directory to the root node.
	add(root, rootDir)

	// If a directory was selected, open it.
	tree.SetSelectedFunc(func(node *tview.TreeNode) {
		reference := node.GetReference()
		if reference == nil {
			return // Selecting the root node does nothing.
		}
		children := node.GetChildren()
		if len(children) == 0 {
			// Load and show files in this directory.
			path := reference.(string)
			add(node, path)
		} else {
			// Collapse if visible, expand if collapsed.
			node.SetExpanded(!node.IsExpanded())
		}
	})

	if err := tview.NewApplication().SetRoot(tree, true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Go tview Read-Only TableContent Implementation Example
DESCRIPTION: Illustrates how to implement a read-only `TableContent` using the `tview.TableContentReadOnly` struct. This example shows the minimal required methods (`GetCell`, `GetRowCount`, `GetColumnCount`) for displaying data, with write operations handled by the embedded `TableContentReadOnly` struct.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/VirtualTable.md#_snippet_1

LANGUAGE: Go
CODE:
```
type MyData struct {
	tview.TableContentReadOnly
}

func (d *MyData) GetCell(row, column int) *tview.TableCell {
	// Return content at (row,column).
}

func (d *MyData) GetRowCount() int {
	// Return total number of rows.
}

func (d *MyData) GetColumnCount() int {
	// Return total number of columns.
}
```

----------------------------------------

TITLE: Implement Mouse Handler for tview Composed Primitive (Go)
DESCRIPTION: This Go function provides an example of implementing the `MouseHandler()` for a `tview` primitive composed of other primitives. It first checks if the mouse event is within its bounds, then attempts to pass the event to a child primitive, and finally handles any events not consumed by the child.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_12

LANGUAGE: Go
CODE:
```
func (p *MyPrimitive) MouseHandler() func(action tview.MouseAction, event *tcell.EventMouse, setFocus func(p tview.Primitive)) (consumed bool, capture Primitive) {
	return p.WrapMouseHandler(func(action tview.MouseAction, event *tcell.EventMouse, setFocus func(p tview.Primitive)) (consumed bool, capture Primitive) {
		if !p.InRect(event.Position()) {
			return false, nil
		}

		// Pass mouse events down.
		if p.childPrimitive != nil {
			consumed, capture = p.childPrimitive.MouseHandler()(action, event, setFocus)
			if consumed {
				return
			}
		}

		// ...handle mouse events not directed to the child primitive...
		return true, nil
	})
}
```

----------------------------------------

TITLE: Create a Basic Checkbox with tview
DESCRIPTION: This Go snippet initializes a new `tview` application and creates a simple checkbox widget. It sets the label for the checkbox and then runs the application, setting the checkbox as the root element and focus target. The application panics on error during startup.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Checkbox.md#_snippet_0

LANGUAGE: Go
CODE:
```
package main

import "github.com/rivo/tview"

func main() {
	app := tview.NewApplication()
	checkbox := tview.NewCheckbox().SetLabel("Hit Enter to check box: ")
	if err := app.SetRoot(checkbox, true).SetFocus(checkbox).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Conditional Focus Delegation in tview Composed Primitive (Go)
DESCRIPTION: This Go example provides a more flexible `Focus()` implementation for `tview` primitives that can contain children. It delegates focus to a child primitive if one exists, otherwise, it allows the parent primitive (or its embedded `Box`) to retain focus, handling cases where the parent needs focus itself.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_9

LANGUAGE: Go
CODE:
```
func (p *MyPrimitive) Focus(delegate func(p Primitive)) {
	if d.childPrimitive != nil {
		delegate(d.childPrimitive)
	} else {
		p.Box.Focus(delegate)
	}
}
```

----------------------------------------

TITLE: Go tview Rolling Window TableContent Implementation
DESCRIPTION: Provides an example of implementing `TableContent` for a rolling window data structure, such as a log file. This `MyLog` struct demonstrates how to manage a fixed-size buffer and dynamically update its contents, ensuring only the most recent data is displayed in the `tview` table.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/VirtualTable.md#_snippet_2

LANGUAGE: Go
CODE:
```
type MyLog struct {
	tview.TableContentReadOnly

	// Nevermind the hard-coded values, this is just an example.
	data       [200][5]string
	startIndex int
}

func (d *MyLog) GetCell(row, column int) *tview.TableCell {
	return tview.NewTableCell(d.data[(row+d.startIndex)%200][column])
}

func (d *MyLog) GetRowCount() int {
	return 200
}

func (d *MyLog) GetColumnCount() int {
	return 5
}

func (d *MyLog) AppendRow(row [5]string) {
	d.data[d.startIndex] = row
	d.startIndex = (d.startIndex + 1) % 200
}
```

----------------------------------------

TITLE: Go tview Multi-line Text Area Demo
DESCRIPTION: This Go program demonstrates the creation and functionality of a multi-line text area using the `tview` library. It includes features like real-time cursor position display, dynamic help screens for navigation and editing shortcuts, and basic text manipulation (copy, cut, paste, undo/redo). The application uses `tcell` for terminal interaction and `tview` for UI components, providing a comprehensive example of a terminal-based text editor.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/TextArea.md#_snippet_0

LANGUAGE: Go
CODE:
```
// Demo code for the TextArea primitive.
package main

import (
	"fmt"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

func main() {
	app := tview.NewApplication()

	textArea := tview.NewTextArea().
		SetPlaceholder("Enter text here...")
	textArea.SetTitle("Text Area").SetBorder(true)
	helpInfo := tview.NewTextView().
		SetText(" Press F1 for help, press Ctrl-C to exit")
	position := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignRight)
	pages := tview.NewPages()

	updateInfos := func() {
		fromRow, fromColumn, toRow, toColumn := textArea.GetCursor()
		if fromRow == toRow && fromColumn == toColumn {
			position.SetText(fmt.Sprintf("Row: [yellow]%d[white], Column: [yellow]%d ", fromRow, fromColumn))
		} else {
			position.SetText(fmt.Sprintf("[red]From[white] Row: [yellow]%d[white], Column: [yellow]%d[white] - [red]To[white] Row: [yellow]%d[white], To Column: [yellow]%d ", fromRow, fromColumn, toRow, toColumn))
		}
	}

	textArea.SetMovedFunc(updateInfos)
	updateInfos()

	mainView := tview.NewGrid().
		SetRows(0, 1).
		AddItem(textArea, 0, 0, 1, 2, 0, 0, true).
		AddItem(helpInfo, 1, 0, 1, 1, 0, 0, false).
		AddItem(position, 1, 1, 1, 1, 0, 0, false)

	help1 := tview.NewTextView().
		SetDynamicColors(true).
		SetText(`[green]Navigation

[yellow]Left arrow[white]: Move left.
[yellow]Right arrow[white]: Move right.
[yellow]Down arrow[white]: Move down.
[yellow]Up arrow[white]: Move up.
[yellow]Ctrl-A, Home[white]: Move to the beginning of the current line.
[yellow]Ctrl-E, End[white]: Move to the end of the current line.
[yellow]Ctrl-F, page down[white]: Move down by one page.
[yellow]Ctrl-B, page up[white]: Move up by one page.
[yellow]Alt-Up arrow[white]: Scroll the page up.
[yellow]Alt-Down arrow[white]: Scroll the page down.
[yellow]Alt-Left arrow[white]: Scroll the page to the left.
[yellow]Alt-Right arrow[white]:  Scroll the page to the right.
[yellow]Alt-B, Ctrl-Left arrow[white]: Move back by one word.
[yellow]Alt-F, Ctrl-Right arrow[white]: Move forward by one word.

[blue]Press Enter for more help, press Escape to return.`)
	help2 := tview.NewTextView().
		SetDynamicColors(true).
		SetText(`[green]Editing[white]

Type to enter text.
[yellow]Ctrl-H, Backspace[white]: Delete the left character.
[yellow]Ctrl-D, Delete[white]: Delete the right character.
[yellow]Ctrl-K[white]: Delete until the end of the line.
[yellow]Ctrl-W[white]: Delete the rest of the word.
[yellow]Ctrl-U[white]: Delete the current line.

[blue]Press Enter for more help, press Escape to return.`)
	help3 := tview.NewTextView().
		SetDynamicColors(true).
		SetText(`[green]Selecting Text[white]

Move while holding Shift or drag the mouse.
Double-click to select a word.

[green]Clipboard

[yellow]Ctrl-Q[white]: Copy.
[yellow]Ctrl-X[white]: Cut.
[yellow]Ctrl-V[white]: Paste.
		
[green]Undo

[yellow]Ctrl-Z[white]: Undo.
[yellow]Ctrl-Y[white]: Redo.

[blue]Press Enter for more help, press Escape to return.`)
	help := tview.NewFrame(help1).
		SetBorders(1, 1, 0, 0, 2, 2)
	help.SetBorder(true).
		SetTitle("Help").
		SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
			if event.Key() == tcell.KeyEscape {
				pages.SwitchToPage("main")
				return nil
			} else if event.Key() == tcell.KeyEnter {
				switch {
				case help.GetPrimitive() == help1:
					help.SetPrimitive(help2)
				case help.GetPrimitive() == help2:
					help.SetPrimitive(help3)
				case help.GetPrimitive() == help3:
					help.SetPrimitive(help1)
				}
				return nil
			}
			return event
		})

	pages.AddAndSwitchToPage("main", mainView, true).
		AddPage("help", tview.NewGrid().
			SetColumns(0, 64, 0).
			SetRows(0, 22, 0).
			AddItem(help, 1, 1, 1, 1, 0, 0, true), true, false)

	app.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyF1 {
			pages.ShowPage("help") //TODO: Check when clicking outside help window with the mouse. Then clicking help again.
			return nil
		}
		return event
	})

	if err := app.SetRoot(pages,
		true).EnableMouse(true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Initialize and Configure tview Flexbox Layout in Go
DESCRIPTION: This Go code snippet initializes a `tview` application and constructs a sophisticated flexbox layout. It uses `tview.NewFlex()` to create a main container, then adds multiple `Box` elements and a nested `Flex` container with `FlexRow` direction. Items are configured with relative and absolute widths/heights, demonstrating `AddItem` parameters for proportion, fixed size, and focusability. The application then sets the root and runs, displaying the structured terminal UI.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Flex.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import (
	"github.com/rivo/tview"
)

func main() {
	app := tview.NewApplication()
	flex := tview.NewFlex().
		AddItem(tview.NewBox().SetBorder(true).SetTitle("Left (1/2 x width of Top)"), 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(tview.NewBox().SetBorder(true).SetTitle("Top"), 0, 1, false).
			AddItem(tview.NewBox().SetBorder(true).SetTitle("Middle (3 x height of Top)"), 0, 3, false).
			AddItem(tview.NewBox().SetBorder(true).SetTitle("Bottom (5 rows)"), 5, 1, false), 0, 2, false).
		AddItem(tview.NewBox().SetBorder(true).SetTitle("Right (20 cols)"), 20, 1, false)
	if err := app.SetRoot(flex, true).SetFocus(flex).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Deactivate Specific Keyboard Keys in tview TextView
DESCRIPTION: This example shows how to deactivate a specific keyboard key for a `tview` widget. By returning `nil` from the `SetInputCapture` callback, the default processing for that key is prevented. Here, the 'j' key is deactivated while 'w' is remapped to 'j'.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/CustomKeys.md#_snippet_1

LANGUAGE: Go
CODE:
```
textView.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
	switch event.Rune() {
	case 'w':
		return tcell.NewEventKey(tcell.KeyRune, 'j', tcell.ModNone)
	case 'j':
		return nil
	}
	return event
})
```

----------------------------------------

TITLE: Center a tview primitive using Flex and Pages in Go
DESCRIPTION: This Go example illustrates how to programmatically center any `tview.Primitive` on the screen using a combination of `tview.NewFlex()` for layout and `tview.NewPages()` for layering. It defines a reusable `modal` function that wraps a given primitive, placing it centrally and allowing it to appear on top of background content, offering more customization than the default `Modal`.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Modal.md#_snippet_1

LANGUAGE: Go
CODE:
```
package main

import (
	"strings"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

func main() {
	app := tview.NewApplication()

	// Returns a new primitive which puts the provided primitive in the center and
	// sets its size to the given width and height.
	modal := func(p tview.Primitive, width, height int) tview.Primitive {
		return tview.NewFlex().
			AddItem(nil, 0, 1, false).
			AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
				AddItem(nil, 0, 1, false).
				AddItem(p, height, 1, true).
				AddItem(nil, 0, 1, false), width, 1, true).
			AddItem(nil, 0, 1, false)
	}

	background := tview.NewTextView().
		SetTextColor(tcell.ColorBlue).
		SetText(strings.Repeat("background ", 1000))

	box := tview.NewBox().
		SetBorder(true).
		SetTitle("Centered Box")

	pages := tview.NewPages().
		AddPage("background", background, true, true).
		AddPage("modal", modal(box, 40, 10), true, true)

	if err := app.SetRoot(pages, true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Go: Clear and Write to TextView from Goroutine with QueueUpdate
DESCRIPTION: This Go example shows how to clear a `tview.TextView` and then write new lines to it from a goroutine. Since `TextView.Clear()` is not thread-safe, `app.QueueUpdate()` is used to execute the clear operation safely within the main application loop, ensuring it completes before subsequent writes.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Concurrency.md#_snippet_4

LANGUAGE: go
CODE:
```
go func() {
	app.QueueUpdate(func() {
		textView.Clear()
	})
	fmt.Fprintln(textView, "line1")
	fmt.Fprintln(textView, "line2")
}()
```

----------------------------------------

TITLE: Set Global Keyboard Shortcuts for tview Application
DESCRIPTION: This example demonstrates how to apply global keyboard input capture at the `tview.Application` level. `SetInputCapture` on the application allows intercepting key events before they reach individual widgets. It shows how to stop the application using `Ctrl-Q` and prevent `Ctrl-C` from being processed.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/CustomKeys.md#_snippet_3

LANGUAGE: Go
CODE:
```
app.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
	switch event.Key() {
	case tcell.KeyCtrlQ:
		app.Stop()
	case tcell.KeyCtrlC:
		return nil
	}
	return event
})
```

----------------------------------------

TITLE: Go: Update tview Modal with Current Time using QueueUpdateDraw
DESCRIPTION: This Go example demonstrates how to update a `tview.Modal` component with the current time. It uses a goroutine to periodically call `app.QueueUpdateDraw` to safely update the UI from a background thread, ensuring the time displayed is always current.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Timer.md#_snippet_0

LANGUAGE: Go
CODE:
```
// Demo code for a timer based update
package main

import (
	"fmt"
	"time"

	"github.com/rivo/tview"
)

const refreshInterval = 500 * time.Millisecond

var (
	view *tview.Modal
	app  *tview.Application
)

func currentTimeString() string {
	t := time.Now()
	return fmt.Sprintf(t.Format("Current time is 15:04:05"))
}

func updateTime() {
	for {
		time.Sleep(refreshInterval)
		app.QueueUpdateDraw(func() {
			view.SetText(currentTimeString())
		})
	}
}

func main() {
	app = tview.NewApplication()
	view = tview.NewModal().
		SetText(currentTimeString()).
		AddButtons([]string{"Quit", "Cancel"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			if buttonLabel == "Quit" {
				app.Stop()
			}
		})

	go updateTime()
	if err := app.SetRoot(view, false).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Implement Custom Logic for Keyboard Keys in tview Widget
DESCRIPTION: This snippet illustrates how to assign custom functionality to any keyboard key within a `tview` widget. The `SetInputCapture` callback is invoked for each key press, allowing direct manipulation of the widget. In this example, pressing the Escape key clears the `TextView` content.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/CustomKeys.md#_snippet_2

LANGUAGE: Go
CODE:
```
textView.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
	if event.Key() == tcell.KeyEscape {
		textView.Clear()
		return nil
	}
	return event
})
```

----------------------------------------

TITLE: Go: Display Current Time in tview Box using SetDrawFunc and app.Draw()
DESCRIPTION: This Go example illustrates an alternative method for displaying the current time in a `tview.Box`. It leverages `SetDrawFunc` to define a custom drawing routine and triggers screen refreshes by calling `app.Draw()` from a separate goroutine, eliminating the need for `QueueUpdateDraw`.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Timer.md#_snippet_1

LANGUAGE: Go
CODE:
```
// Demo code for a timer based update
package main

import (
	"time"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

const refreshInterval = 500 * time.Millisecond

var (
	view *tview.Box
	app  *tview.Application
)

func drawTime(screen tcell.Screen, x int, y int, width int, height int) (int, int, int, int) {
	timeStr := time.Now().Format("Current time is 15:04:05")
	tview.Print(screen, timeStr, x, height/2, width, tview.AlignCenter, tcell.ColorLime)
	return 0, 0, 0, 0
}

func refresh() {
	tick := time.NewTicker(refreshInterval)
	for {
		select {
		case <-tick.C:
			app.Draw()
		}
	}
}

func main() {
	app = tview.NewApplication()
	view = tview.NewBox().SetDrawFunc(drawTime)

	go refresh()
	if err := app.SetRoot(view, true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Go Tview Image Primitive Demo
DESCRIPTION: This Go program provides a comprehensive demonstration of the `tview.Image` primitive. It allows users to switch between different images (photo, graphics), adjust the color depth (2, 8, 256 colors, true-color), and apply various dithering algorithms (None, Floyd-Steinberg). The application uses `tview` for the UI, `tcell` for terminal interaction, and standard Go image packages for decoding image data from Base64 strings.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Image.md#_snippet_0

LANGUAGE: Go
CODE:
```
// Demo code for the Image primitive.
package main

import (
	"bytes"
	"encoding/base64"

	"image/jpeg"
	"image/png"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

const (
	beach = `see link below for the entire Base64 payload`
	chart = `see link below for the entire Base64 payload`
)

func main() {
	app := tview.NewApplication()

	image := tview.NewImage()
	b, _ := base64.StdEncoding.DecodeString(beach)
	photo, _ := jpeg.Decode(bytes.NewReader(b))
	b, _ = base64.StdEncoding.DecodeString(chart)
	graphics, _ := png.Decode(bytes.NewReader(b))
	image.SetImage(photo)

	imgType := tview.NewList().
		ShowSecondaryText(false).
		AddItem("Photo", "", 0, func() { image.SetImage(photo) }).
		AddItem("Graphics", "", 0, func() { image.SetImage(graphics) })
	imgType.SetTitle("Image Type").SetBorder(true)

	colors := tview.NewList().
		ShowSecondaryText(false).
		AddItem("2 colors", "", 0, func() { image.SetColors(2) }).
		AddItem("8 colors", "", 0, func() { image.SetColors(8) }).
		AddItem("256 colors", "", 0, func() { image.SetColors(256) }).
		AddItem("True-color", "", 0, func() { image.SetColors(tview.TrueColor) })
	colors.SetTitle("Colors").SetBorder(true)
	for i, c := range []int{2, 8, 256, tview.TrueColor} {
		if c == image.GetColors() {
			colors.SetCurrentItem(i)
			break
		}
	}

	dithering := tview.NewList().
		ShowSecondaryText(false).
		AddItem("None", "", 0, func() { image.SetDithering(tview.DitheringNone) }).
		AddItem("Floyd-Steinberg", "", 0, func() { image.SetDithering(tview.DitheringFloydSteinberg) }).
		SetCurrentItem(1)
	dithering.SetTitle("Dithering").SetBorder(true)

	selections := []*tview.Box{imgType.Box, colors.Box, dithering.Box}
	for i, box := range selections {
		(func(index int) {
			box.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
				switch event.Key() {
				case tcell.KeyTab:
					app.SetFocus(selections[(index+1)%len(selections)])
					return nil
				case tcell.KeyBacktab:
					app.SetFocus(selections[(index+len(selections)-1)%len(selections)])
					return nil
				}
				return event
			})
		})(i)
	}

	grid := tview.NewGrid().
		SetBorders(false).
		SetColumns(18, -1).
		SetRows(4, 6, 4, -1).
		AddItem(imgType, 0, 0, 1, 1, 0, 0, true).
		AddItem(colors, 1, 0, 1, 1, 0, 0, false).
		AddItem(dithering, 2, 0, 1, 1, 0, 0, false).
		AddItem(image, 0, 1, 4, 1, 0, 0, false)

	if err := app.SetRoot(grid, true).EnableMouse(true).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Initialize and Display a tview DropDown in Go
DESCRIPTION: This Go code snippet demonstrates how to create a basic interactive dropdown menu using the `github.com/rivo/tview` library. It initializes a new `tview` application, creates a `DropDown` instance with a label and predefined options, and then sets it as the root element of the application, making it focusable and runnable.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/DropDown.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import "github.com/rivo/tview"

func main() {
	app := tview.NewApplication()
	dropdown := tview.NewDropDown().
		SetLabel("Select an option (hit Enter): ").
		SetOptions([]string{"First", "Second", "Third", "Fourth", "Fifth"}, nil)
	if err := app.SetRoot(dropdown, true).SetFocus(dropdown).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: tview Concurrency and Race Conditions
DESCRIPTION: Explains the challenges of direct data access in `tview` primitives and the potential for race conditions when interacting with `Application.Draw()`. It also notes that getters/setters generally do not synchronize access.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Concurrency.md#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Type: TableCell
  Description: Fields can be directly modified, leading to race conditions if accessed concurrently with Application.Draw().

Method: Application.Draw()
  Description: Reads from objects to refresh the screen. Concurrent access with data modification can cause race conditions.

Method: TreeNode.ExpandAll()
  Description: Operates on an entire tree of objects; not protected from data races by local mutexes.
```

----------------------------------------

TITLE: Implement Draw Method for tview RadioButtons Primitive in Go
DESCRIPTION: This Go function implements the `Draw` method for the `RadioButtons` primitive, responsible for rendering its visual representation. It calls `r.Box.DrawForSubclass` to handle basic box drawing, then iterates through options to draw radio buttons and text using `tview.Print`, ensuring content fits within the inner rectangle.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_5

LANGUAGE: Go
CODE:
```
func (r *RadioButtons) Draw(screen tcell.Screen) {
	r.Box.DrawForSubclass(screen, r)
	x, y, width, height := r.GetInnerRect()

	for index, option := range r.options {
		if index >= height {
			break
		}
		radioButton := "\u25ef" // Unchecked.
		if index == r.currentOption {
			radioButton = "\u25c9" // Checked.
		}
		line := fmt.Sprintf(`%s[white]  %s`, radioButton, option)
		tview.Print(screen, line, x, y+index, width, tview.AlignLeft, tcell.ColorYellow)
	}
}
```

----------------------------------------

TITLE: Create Responsive Grid Layout with tview in Go
DESCRIPTION: This Go code snippet illustrates the use of `github.com/rivo/tview` to construct a responsive grid-based terminal user interface. It defines a main content area, menu, and sidebar, demonstrating how to dynamically hide the menu and sidebar when the terminal width falls below 100 characters, ensuring adaptability to different screen sizes.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Grid.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import (
	"github.com/rivo/tview"
)

func main() {
	newPrimitive := func(text string) tview.Primitive {
		return tview.NewTextView().
			SetTextAlign(tview.AlignCenter).
			SetText(text)
	}
	menu := newPrimitive("Menu")
	main := newPrimitive("Main content")
	sideBar := newPrimitive("Side Bar")

	grid := tview.NewGrid().
		SetRows(3, 0, 3).
		SetColumns(30, 0, 30).
		SetBorders(true).
		AddItem(newPrimitive("Header"), 0, 0, 1, 3, 0, 0, false).
		AddItem(newPrimitive("Footer"), 2, 0, 1, 3, 0, 0, false)

	// Layout for screens narrower than 100 cells (menu and side bar are hidden).
	grid.AddItem(menu, 0, 0, 0, 0, 0, 0, false).
		AddItem(main, 1, 0, 1, 3, 0, 0, false).
		AddItem(sideBar, 0, 0, 0, 0, 0, 0, false)

	// Layout for screens wider than 100 cells.
	grid.AddItem(menu, 1, 0, 1, 1, 0, 100, false).
		AddItem(main, 1, 1, 1, 1, 0, 100, false).
		AddItem(sideBar, 1, 2, 1, 1, 0, 100, false)

	if err := tview.NewApplication().SetRoot(grid, true).SetFocus(grid).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: Install tview Go package
DESCRIPTION: Command to add the tview package to your Go project, fetching it from GitHub.
SOURCE: https://github.com/rivo/tview/blob/master/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
go get github.com/rivo/tview@master
```

----------------------------------------

TITLE: Implement Input Handler for tview Composed Primitive (Go)
DESCRIPTION: This Go snippet demonstrates how to implement the `InputHandler()` for a `tview` primitive that contains child primitives. It shows how to conditionally pass keyboard events down to the child primitive if the parent does not have focus, allowing the child to process its own input.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Primitives.md#_snippet_11

LANGUAGE: Go
CODE:
```
func (p *MyPrimitive) InputHandler() func(event *tcell.EventKey, setFocus func(p tview.Primitive)) {
	return p.WrapInputHandler(func(event *tcell.EventKey, setFocus func(p tview.Primitive)) {
		if !p.hasFocus {
			// Pass event on to child primitive.
			if p.childPrimitive != nil && p.childPrimitive.HasFocus() {
				if handler := p.childPrimitive.InputHandler(); handler != nil {
					handler(event, setFocus)
				}
			}
			return
		}
		// ...handle key events not forwarded to the child primitive...
	}
}
```

----------------------------------------

TITLE: Go: Implement Multi-color TextView with Dynamic Selections
DESCRIPTION: This Go program initializes a `tview.TextView` to display a corporate lorem ipsum text. It dynamically colors specific words ('the' in red, 'to' as selectable regions) and allows users to navigate through these selectable regions using the Enter, Tab, and Backtab keys. The `tview` and `tcell` libraries are used for the UI.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/TextView.md#_snippet_0

LANGUAGE: go
CODE:
```
package main

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

const corporate = `Leverage agile frameworks to provide a robust synopsis for high level overviews. Iterative approaches to corporate strategy foster collaborative thinking to further the overall value proposition. Organically grow the holistic world view of disruptive innovation via workplace diversity and empowerment.

Bring to the table win-win survival strategies to ensure proactive domination. At the end of the day, going forward, a new normal that has evolved from generation X is on the runway heading towards a streamlined cloud solution. User generated content in real-time will have multiple touchpoints for offshoring.

Capitalize on low hanging fruit to identify a ballpark value added activity to beta test. Override the digital divide with additional clickthroughs from DevOps. Nanotechnology immersion along the information highway will close the loop on focusing solely on the bottom line.

[yellow]Press Enter, then Tab/Backtab for word selections`

func main() {
	app := tview.NewApplication()
	textView := tview.NewTextView().
		SetDynamicColors(true).
		SetRegions(true).
		SetChangedFunc(func() {
			app.Draw()
		})
	numSelections := 0
	go func() {
		for _, word := range strings.Split(corporate, " ") {
			if word == "the" {
				word = "[red]the[white]"
			}
			if word == "to" {
				word = fmt.Sprintf(`["%d"]to[""]`, numSelections)
				numSelections++
			}
			fmt.Fprintf(textView, "%s ", word)
			time.Sleep(200 * time.Millisecond)
		}
	}()
	textView.SetDoneFunc(func(key tcell.Key) {
		currentSelection := textView.GetHighlights()
		if key == tcell.KeyEnter {
			if len(currentSelection) > 0 {
				textView.Highlight()
			} else {
				textView.Highlight("0").ScrollToHighlight()
			}
		} else if len(currentSelection) > 0 {
			index, _ := strconv.Atoi(currentSelection[0])
			if key == tcell.KeyTab {
				index = (index + 1) % numSelections
			} else if key == tcell.KeyBacktab {
				index = (index - 1 + numSelections) % numSelections
			} else {
				return
			}
			textView.Highlight(strconv.Itoa(index)).ScrollToHighlight()
		}
	})
	textView.SetBorder(true)
	if err := app.SetRoot(textView, true).SetFocus(textView).Run(); err != nil {
		panic(err)
	}
}
```

----------------------------------------

TITLE: tview TextView Concurrency and Batch Operations
DESCRIPTION: Details the `TextView` primitive's implementation of `io.Writer` and its specific concurrency behavior. Explains that `TextView.SetChangedFunc()` handlers are invoked from a different goroutine, requiring careful handling, but `Application.Draw()` and `TextView.HasFocus()` are safe to call. Introduces `TextView.BatchWriter()` for performance-optimized, manual-locking writes.
SOURCE: https://github.com/rivo/tview/blob/master/__wiki__/Concurrency.md#_snippet_2

LANGUAGE: APIDOC
CODE:
```
Type: TextView
  Implements: io.Writer
  Description: Common to write to from a different goroutine.

Interface: io.Writer
  Description: Go standard library interface implemented by tview.TextView.

Method: TextView.SetChangedFunc(handler func())
  Description: Handler invoked from a different goroutine.
  Safe Calls within Handler: Application.Draw(), TextView.HasFocus()
  Other Actions within Handler: Should queue an update using Application.QueueUpdate().

Method: TextView.HasFocus() bool
  Description: Safe to call from TextView.SetChangedFunc() handler.

Method: TextView.BatchWriter() io.Writer
  Description: Returns an io.Writer that allows writing without locking for improved performance. Requires manual locking management (by closing the batch writer). The "changed" handler will still be called in a separate goroutine.
```
