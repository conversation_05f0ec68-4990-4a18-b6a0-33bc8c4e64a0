package copier

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"crypto/rand"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"

	"bella/pkg/checksum"
	"bella/pkg/device"
	"bella/pkg/progress"
)

// promptForFileAction prompts the user when a file already exists.
func promptForFileAction(outputPath string, isUI bool) (string, error) {
	if isUI {
		// UI mode has its own dialog, but we can default to overwrite for its logic path.
		return "overwrite", nil
	}

	fmt.Printf("File '%s' already exists.\n", outputPath)
	fmt.Print("Choose action: (o)verwrite, (a)ppend, (v)erify only, (c)ancel [o/a/v/c]: ")

	reader := bufio.NewReader(os.Stdin)
	response, err := reader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("failed to read user input: %w", err)
	}

	response = strings.ToLower(strings.TrimSpace(response))
	switch response {
	case "o", "overwrite":
		return "overwrite", nil
	case "a", "append":
		return "append", nil
	case "v", "verify":
		return "verify", nil
	case "c", "cancel":
		return "cancel", nil
	default:
		return "cancel", fmt.Errorf("invalid choice: %s", response)
	}
}

func Execute(cfg *Config) error {
	if cfg.DryRun {
		fmt.Printf("Dry run: would perform operation with config: %+v\n", cfg)
		return nil
	}

	// Automatically detect directory copy.
	if cfg.Operation == OpCopy {
		info, err := os.Stat(cfg.Input)
		if err != nil {
			return fmt.Errorf("could not access input '%s': %w", cfg.Input, err)
		}
		if info.IsDir() {
			return copyDirectory(cfg)
		}
		// It's a file, proceed as before.
		return copyFile(cfg)
	}

	// Handle other operations
	switch cfg.Operation {
	case OpWipe:
		return doWipe(cfg)
	case OpVerify:
		return doVerify(cfg)
	default:
		return fmt.Errorf("no operation specified or recognized")
	}
}

// copyFile is the central point for overwrite/append checks for single files.
func copyFile(cfg *Config) error {
	// Check for existing final output file BEFORE any temp files are made.
	if _, err := os.Stat(cfg.Output); err == nil && !cfg.Append {
		action, err := promptForFileAction(cfg.Output, cfg.IsUIMode)
		if err != nil {
			return err
		}
		switch action {
		case "cancel":
			return fmt.Errorf("operation cancelled by user")
		case "append":
			cfg.Append = true // Set append mode if chosen
		case "verify":
			// Switch the entire operation to a verify-only task
			cfg.Operation = OpVerify
			return doVerify(cfg)
		case "overwrite":
			// Proceed with the copy
		}
	}

	if cfg.ShouldUseMultiStage() {
		return copyFileMultiStage(cfg)
	}
	return copyFileSingleStage(cfg)
}

func copyFileMultiStage(cfg *Config) error {
	finalOutput := cfg.Output
	tempOutput := finalOutput + ".tmp.bella"
	failedOutput := finalOutput + ".FAILED"

	// Clean up any previous failed attempts before starting.
	os.Remove(failedOutput)

	// Create a config for the raw copy stage.
	rawCfg := *cfg
	rawCfg.Compression = "none"
	rawCfg.Verify = false
	rawCfg.Output = tempOutput

	err := copyFileSingleStage(&rawCfg)
	if err != nil {
		os.Remove(tempOutput)
		return fmt.Errorf("stage 1 (copy) failed: %w", err)
	}

	if tempFile, err := os.OpenFile(tempOutput, os.O_WRONLY, 0); err == nil {
		tempFile.Sync()
		tempFile.Close()
	}

	// Verification stage
	if cfg.Verify {
		verifyCfg := *cfg
		verifyCfg.Operation = OpVerify
		verifyCfg.Input = cfg.Input
		verifyCfg.Output = tempOutput
		verifyCfg.Compression = "none"
		verifyCfg.Checksum = ""

		err = doVerify(&verifyCfg)
		if err != nil {
			os.Rename(tempOutput, failedOutput)
			return fmt.Errorf("stage 2 (verification) failed: %w. The incomplete file has been saved as %s", err, failedOutput)
		}
	}

	// Compression stage
	if cfg.Compression == "compress" {
		if cfg.Append {
			err = appendCompressedFile(tempOutput, finalOutput, cfg)
		} else {
			err = compressFile(tempOutput, finalOutput, cfg)
		}
		if err != nil {
			os.Rename(tempOutput, failedOutput)
			return fmt.Errorf("stage 3 (compression) failed: %w. The incomplete file has been saved as %s", err, failedOutput)
		}
		os.Remove(tempOutput)
	} else {
		if cfg.Append {
			err = appendTempFileToFinal(tempOutput, finalOutput)
		} else {
			err = os.Rename(tempOutput, finalOutput)
		}
		if err != nil {
			return fmt.Errorf("failed to finalize output file: %w", err)
		}
		if cfg.Append {
			os.Remove(tempOutput)
		}
	}

	// Apply metadata (permissions and ownership) for multi-stage file copies
	if cfg.PreserveAttributes {
		if err := applyMetadata(cfg.Input, finalOutput); err != nil {
			log.Printf("Warning: failed to apply metadata to '%s': %v", finalOutput, err)
		}
	}

	return nil
}

func appendTempFileToFinal(tempPath, finalPath string) error {
	tempFile, err := os.Open(tempPath)
	if err != nil {
		return fmt.Errorf("failed to open temporary file for append: %w", err)
	}
	defer tempFile.Close()

	finalFile, err := os.OpenFile(finalPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open final file for append: %w", err)
	}
	defer finalFile.Close()

	_, err = io.Copy(finalFile, tempFile)
	if err != nil {
		return fmt.Errorf("failed to append temp file content: %w", err)
	}

	return finalFile.Sync()
}

func appendCompressedFile(tempPath, finalPath string, cfg *Config) error {
	tempFile, err := os.Open(tempPath)
	if err != nil {
		return fmt.Errorf("failed to open temporary file for compression: %w", err)
	}
	defer tempFile.Close()

	finalFile, err := os.OpenFile(finalPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open final file for compressed append: %w", err)
	}
	defer finalFile.Close()

	var writer io.Writer = finalFile
	if cfg.CompressionType == "gzip" {
		gw, err := gzip.NewWriterLevel(writer, gzip.DefaultCompression)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer for append: %w", err)
		}
		defer gw.Close()
		writer = gw
	}

	_, err = io.Copy(writer, tempFile)
	if err != nil {
		return fmt.Errorf("failed to compress and append temp file content: %w", err)
	}

	return finalFile.Sync()
}

func copyFileSingleStage(cfg *Config) error {
	in, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open input '%s': %w", cfg.Input, err)
	}
	defer in.Close()

	openFlags := os.O_CREATE | os.O_WRONLY
	if cfg.Append {
		openFlags |= os.O_APPEND
	} else {
		openFlags |= os.O_TRUNC
	}
	out, err := os.OpenFile(cfg.Output, openFlags, 0666)
	if err != nil {
		return fmt.Errorf("failed to open output '%s': %w", cfg.Output, err)
	}
	defer out.Close()

	totalSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		log.Printf("Warning: could not determine input size: %v\n", err)
		totalSize = 0
	}

	if cfg.Count > 0 {
		countSize := int64(cfg.Count) * int64(cfg.BlockSize)
		if totalSize == 0 || countSize < totalSize {
			totalSize = countSize
		}
	}

	if cfg.ShouldUseCopyOffload() {
		reporter := progress.NewReporter("Kernel Offload", cfg.ProgressChan)
		if cfg.Progress {
			reporter.Update(0, totalSize)
		}

		// Use the new progress-aware kernel offload
		progressCallback := func(written, total int64) {
			if cfg.Progress && reporter != nil {
				reporter.Update(written, total)
			}
		}

		written, err := device.CopyFileRangeWithProgress(in, out, totalSize, progressCallback)
		if err == nil {
			if cfg.Progress {
				reporter.Finish(written)
			}
			return nil
		}
		if err != syscall.ENOSYS {
			return fmt.Errorf("kernel copy failed: %w", err)
		}
		log.Println("Kernel offload not supported, falling back to standard copy.")
	}

	if cfg.Skip > 0 {
		if _, err := in.Seek(cfg.Skip*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to skip in input: %w", err)
		}
	}
	if cfg.Seek > 0 && !cfg.Append {
		if _, err := out.Seek(cfg.Seek*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek in output: %w", err)
		}
	}

	var reader io.Reader = in
	if cfg.Compression == "decompress" || (cfg.Compression == "auto" && strings.HasSuffix(cfg.Input, ".gz")) {
		if cfg.CompressionType == "gzip" {
			gr, err := gzip.NewReader(reader)
			if err != nil {
				return fmt.Errorf("failed to create gzip reader: %w", err)
			}
			defer gr.Close()
			reader = gr
			totalSize = 0
		}
	}

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Copying", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	bufReader := bufio.NewReaderSize(reader, cfg.BlockSize)
	err = copyLoop(bufReader, out, out, cfg, nil, totalSize, reporter)
	if err != nil {
		return err
	}
	if reporter != nil {
		reporter.Finish(totalSize)
	}

	// Apply metadata (permissions and ownership) for single file copies
	if cfg.PreserveAttributes {
		if err := applyMetadata(cfg.Input, cfg.Output); err != nil {
			log.Printf("Warning: failed to apply metadata to '%s': %v", cfg.Output, err)
		}
	}

	return nil
}

func compressFile(inputPath, outputPath string, cfg *Config) error {
	in, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input for compression: %w", err)
	}
	defer in.Close()

	out, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create compressed output: %w", err)
	}
	defer out.Close()

	info, err := in.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info for compression: %w", err)
	}
	totalSize := info.Size()

	var writer io.Writer = out
	if cfg.CompressionType == "gzip" {
		gw, err := gzip.NewWriterLevel(writer, gzip.DefaultCompression)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		defer gw.Close()
		writer = gw
	}

	reporter := progress.NewReporter("Compressing", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	buf := make([]byte, cfg.BlockSize)
	var written int64
	for {
		n, err := in.Read(buf)
		if n > 0 {
			if _, wErr := writer.Write(buf[:n]); wErr != nil {
				return fmt.Errorf("compression write error: %w", wErr)
			}
			written += int64(n)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("compression read error: %w", err)
		}
	}

	if cfg.Progress {
		reporter.Finish(written)
	}
	return nil
}

// copyLoop is the heart of the copy process. It now takes an atomic counter and a reporter for live progress.
func copyLoop(r io.Reader, w io.Writer, outFile *os.File, cfg *Config, copiedSize *int64, totalSize int64, reporter *progress.Reporter) error {
	buf := make([]byte, cfg.BlockSize)
	blocksCopied := 0

	var hasher *checksum.Hasher
	if cfg.Checksum != "" {
		var err error
		hasher, err = checksum.NewHasher(cfg.Checksum)
		if err != nil {
			return fmt.Errorf("failed to initialize checksum: %w", err)
		}
	}

	// If this is a single file copy (not part of a directory), initialize its own atomic counter.
	if copiedSize == nil {
		var singleFileSize int64
		copiedSize = &singleFileSize
	}

	for {
		if cfg.Count > 0 && blocksCopied >= cfg.Count {
			break
		}

		n, err := r.Read(buf)
		if n > 0 {
			if hasher != nil {
				if _, hashErr := hasher.Write(buf[:n]); hashErr != nil {
					return fmt.Errorf("checksum calculation error: %w", hashErr)
				}
			}

			if cfg.Sparse && isAllZeros(buf[:n]) {
				if _, seekErr := outFile.Seek(int64(n), io.SeekCurrent); seekErr != nil {
					if _, wErr := w.Write(buf[:n]); wErr != nil {
						return fmt.Errorf("sparse seek and fallback write failed: %w", wErr)
					}
				}
			} else {
				if _, wErr := w.Write(buf[:n]); wErr != nil {
					return fmt.Errorf("write error: %w", wErr)
				}
			}

			// Atomically update the total bytes copied and report progress.
			atomic.AddInt64(copiedSize, int64(n))
			if reporter != nil {
				reporter.Update(atomic.LoadInt64(copiedSize), totalSize)
			}

			blocksCopied++
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			if cfg.SkipBadSectors {
				log.Printf("Read error, skipping: %v\n", err)
				zeroBuf := make([]byte, cfg.BlockSize)
				if _, wErr := w.Write(zeroBuf); wErr != nil {
					return fmt.Errorf("error writing zero block for bad sector: %w", wErr)
				}
				atomic.AddInt64(copiedSize, int64(cfg.BlockSize))
				if reporter != nil {
					reporter.Update(atomic.LoadInt64(copiedSize), totalSize)
				}
				continue
			}
			return fmt.Errorf("read error: %w", err)
		}
	}

	if outFile != nil {
		outFile.Sync()
	}

	if f, ok := w.(io.Closer); ok {
		f.Close()
	}

	// Checksum file output logic
	if hasher != nil {
		checksumValue := fmt.Sprintf("%x", hasher.Sum(nil))
		// For single files, write a checksum file.
		if !cfg.isRecursive {
			checksumFilename := cfg.Output + "." + strings.ToLower(string(hasher.Algorithm))
			content := fmt.Sprintf("%s  %s\n", checksumValue, filepath.Base(cfg.Output))
			if err := os.WriteFile(checksumFilename, []byte(content), 0664); err != nil {
				log.Printf("Warning: Failed to write checksum file: %v", err)
			}
			fmt.Printf("Checksum file created: %s\n", checksumFilename)
		} else {
			// For now, for directory copies, we just print the checksum of the TAR stream if one were created.
			// True per-file manifest is a much larger feature.
			log.Printf("Directory copy checksum (%s): %s", string(hasher.Algorithm), checksumValue)
		}
	}

	return nil
}

// copyFileForWorker prepares and executes a copy operation for a single file within a parallel directory copy.
func copyFileForWorker(srcPath, destPath string, mainCfg *Config, copiedSize *int64, totalSize int64, reporter *progress.Reporter) error {
	src, err := os.Open(srcPath)
	if err != nil {
		return fmt.Errorf("failed to open input '%s': %w", srcPath, err)
	}
	defer src.Close()

	// Use a temporary file for the copy to ensure atomicity
	tempDestPath := destPath + ".tmp.bella"
	out, err := os.OpenFile(tempDestPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0666)
	if err != nil {
		return fmt.Errorf("failed to open temporary output '%s': %w", tempDestPath, err)
	}

	// The copyLoop now gets the atomic counter and reporter to provide live updates.
	err = copyLoop(src, out, out, mainCfg, copiedSize, totalSize, reporter)

	// Close the file handle before trying to move/set metadata
	out.Close()

	if err != nil {
		os.Remove(tempDestPath) // Clean up failed temp file
		return err
	}

	// Apply metadata (permissions and ownership)
	if mainCfg.PreserveAttributes {
		if err := applyMetadata(srcPath, tempDestPath); err != nil {
			log.Printf("Warning: failed to apply full metadata to '%s': %v", destPath, err)
		}
	}

	// Atomically move the completed file into place
	if err := os.Rename(tempDestPath, destPath); err != nil {
		return fmt.Errorf("failed to move temporary file to final destination: %w", err)
	}

	return nil
}

// copyDirectory uses a worker pool to copy files in parallel.
func copyDirectory(cfg *Config) error {
	log.Printf("Preparing to recursively copy directory %s to %s with %d threads\n", cfg.Input, cfg.Output, cfg.Threads)

	// --- STAGE 1: SCANNING and PLANNING ---
	type fileJob struct {
		srcPath  string
		destPath string
		size     int64
		mode     os.FileMode
	}
	var fileJobs []fileJob
	var totalSize int64

	err := filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)

		if d.IsDir() {
			return os.MkdirAll(destPath, 0755) // Pre-create all directories sequentially
		}

		info, err := d.Info()
		if err != nil {
			return err
		}

		job := fileJob{srcPath: path, destPath: destPath, size: info.Size(), mode: info.Mode()}
		fileJobs = append(fileJobs, job)
		totalSize += info.Size()
		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to scan source directory: %w", err)
	}

	// --- STAGE 2: PARALLEL COPYING ---
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Copying Directory", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	jobs := make(chan fileJob, len(fileJobs))
	results := make(chan error, len(fileJobs))
	var wg sync.WaitGroup
	var copiedSize int64

	// Start worker goroutines
	for w := 1; w <= cfg.Threads; w++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for job := range jobs {
				// The call is now simpler as the logic is in the helper.
				err := copyFileForWorker(job.srcPath, job.destPath, cfg, &copiedSize, totalSize, reporter)
				results <- err // Send result back, nil on success
			}
		}(w)
	}

	// Feed jobs to workers
	for _, job := range fileJobs {
		jobs <- job
	}
	close(jobs)

	// Wait for results
	var firstErr error
	for i := 0; i < len(fileJobs); i++ {
		err := <-results
		if err != nil && firstErr == nil {
			firstErr = err // Capture the first error
		}
	}
	wg.Wait() // Ensure all workers have finished

	if firstErr != nil {
		return firstErr // Return the first error encountered
	}

	if cfg.Progress && reporter != nil {
		// Final update to ensure the bar hits 100%
		reporter.Update(totalSize, totalSize)
		reporter.Finish(totalSize)
	}
	log.Println("Directory copy stage completed.")

	// --- STAGE 3: SEQUENTIAL VERIFICATION (if requested) ---
	if cfg.Verify {
		log.Println("Starting directory verification stage.")
		if cfg.Progress {
			reporter = progress.NewReporter("Verifying Directory", cfg.ProgressChan)
			reporter.Update(0, totalSize)
		}

		var verifiedSize int64
		for _, job := range fileJobs {
			verifyCfg := *cfg
			verifyCfg.Input = job.srcPath
			verifyCfg.Output = job.destPath
			verifyCfg.Progress = false

			if err := doVerify(&verifyCfg); err != nil {
				return fmt.Errorf("verification failed for file '%s': %w", job.destPath, err)
			}
			verifiedSize += job.size
			if cfg.Progress && reporter != nil {
				reporter.Update(verifiedSize, totalSize)
			}
		}
		if cfg.Progress && reporter != nil {
			reporter.Finish(verifiedSize)
		}
		log.Println("Directory verification stage completed successfully.")
	}

	return nil
}

func doWipe(cfg *Config) error {
	out, err := os.OpenFile(cfg.Output, os.O_WRONLY, 0)
	if err != nil {
		return fmt.Errorf("failed to open output for wiping: %w", err)
	}
	defer out.Close()
	totalSize, err := device.GetDeviceSize(cfg.Output)
	if err != nil {
		log.Printf("Warning: could not determine wipe size: %v\n", err)
		totalSize = 0
	}
	buf := make([]byte, cfg.BlockSize)

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Wipe", cfg.ProgressChan)
	}

	for pass := 1; pass <= cfg.WipePasses; pass++ {
		if cfg.Progress {
			stageName := fmt.Sprintf("Wiping (Pass %d/%d)", pass, cfg.WipePasses)
			reporter.SetStage(stageName)
			reporter.Update(0, totalSize)
		} else {
			fmt.Fprintf(os.Stderr, "Starting pass %d of %d...\n", pass, cfg.WipePasses)
		}

		if _, err := out.Seek(0, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek to start for wipe pass %d: %w", pass, err)
		}
		var written int64 = 0
		for {
			if totalSize > 0 && written >= totalSize {
				break
			}
			if cfg.WipeMode == "random" {
				if _, err := rand.Read(buf); err != nil {
					return fmt.Errorf("failed to generate random data: %w", err)
				}
			} else {
				for i := range buf {
					buf[i] = 0
				}
			}
			n, err := out.Write(buf)
			if n > 0 {
				written += int64(n)
				if cfg.Progress {
					reporter.Update(written, totalSize)
				}
			}
			if err != nil {
				if totalSize > 0 && written >= totalSize {
					break
				}
				return fmt.Errorf("wipe write error: %w", err)
			}
		}
	}

	if cfg.Progress {
		reporter.Finish(totalSize)
	}
	return nil
}

func doVerify(cfg *Config) error {
	src, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open source '%s' for verification: %w", cfg.Input, err)
	}
	defer src.Close()

	if _, err := os.Stat(cfg.Output); os.IsNotExist(err) {
		return fmt.Errorf("verification failed: destination file '%s' does not exist", cfg.Output)
	}

	dst, err := os.Open(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to open destination '%s' for verification: %w", cfg.Output, err)
	}
	defer dst.Close()

	srcSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to get source size for verification: %w", err)
	}

	dstStat, err := dst.Stat()
	if err != nil {
		return fmt.Errorf("failed to stat destination for verification: %w", err)
	}

	isDestDevice := (dstStat.Mode() & os.ModeDevice) != 0

	if !isDestDevice {
		dstSize, err := device.GetDeviceSize(cfg.Output)
		if err != nil {
			return fmt.Errorf("failed to get destination size for verification: %w", err)
		}
		if srcSize != dstSize {
			return fmt.Errorf("verification failed: file sizes differ (source: %d, destination: %d)", srcSize, dstSize)
		}
	} else {
		dstSize, err := device.GetDeviceSize(cfg.Output)
		if err != nil {
			return fmt.Errorf("failed to get destination size for verification: %w", err)
		}
		if srcSize > dstSize {
			return fmt.Errorf("verification failed: destination device too small (source: %d, destination: %d)", srcSize, dstSize)
		}
	}

	totalSize := srcSize
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Verifying", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	srcBuf := make([]byte, cfg.BlockSize)
	dstBuf := make([]byte, cfg.BlockSize)
	var verified int64

	for {
		n, srcErr := src.Read(srcBuf)
		if srcErr != nil && srcErr != io.EOF {
			return fmt.Errorf("error reading source during verification: %w", srcErr)
		}
		if n == 0 {
			break
		}

		_, dstErr := io.ReadFull(dst, dstBuf[:n])
		if dstErr != nil {
			if dstErr == io.ErrUnexpectedEOF || dstErr == io.EOF {
				return fmt.Errorf("verification failed: destination is shorter than source at offset %d", verified)
			}
			return fmt.Errorf("error reading destination during verification: %w", dstErr)
		}

		if !bytes.Equal(srcBuf[:n], dstBuf[:n]) {
			return fmt.Errorf("verification failed: data mismatch at offset %d", verified)
		}

		verified += int64(n)
		if cfg.Progress && reporter != nil {
			reporter.Update(verified, totalSize)
		}

		if srcErr == io.EOF {
			break
		}
	}

	if cfg.Progress {
		reporter.Finish(verified)
	} else {
		fmt.Printf("Verification successful: %s verified.\n", progress.HumanizeBytes(uint64(verified)))
	}

	return nil
}

func isAllZeros(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}

func applyMetadata(srcPath, destPath string) error {
	// Get source file metadata
	info, err := os.Stat(srcPath)
	if err != nil {
		return fmt.Errorf("failed to stat source for metadata: %w", err)
	}

	// 1. Set Permissions
	if err := os.Chmod(destPath, info.Mode()); err != nil {
		log.Printf("Warning: could not set permissions on %s: %v", destPath, err)
	}

	// 2. Set Ownership (UID/GID)
	if stat, ok := info.Sys().(*syscall.Stat_t); ok {
		uid := int(stat.Uid)
		gid := int(stat.Gid)
		if err := os.Chown(destPath, uid, gid); err != nil {
			// This will likely fail if not run as root, which is expected.
			// We log it as a warning rather than a fatal error.
			log.Printf("Warning: could not set ownership on %s (run with sudo?): %v", destPath, err)
		}
	}

	return nil
}
