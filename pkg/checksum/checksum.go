package checksum

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"hash"
	"io"
	"os"
	"strings"
)

// Algorithm represents a supported checksum algorithm
type Algorithm string

const (
	SHA256 Algorithm = "sha256"
	MD5    Algorithm = "md5"
	SHA1   Algorithm = "sha1"
)

// Hasher wraps a hash.Hash with additional metadata
type Hasher struct {
	hash.Hash
	Algorithm Algorithm
}

// NewHasher creates a new hasher for the specified algorithm
func NewHasher(algorithm string) (*Hasher, error) {
	alg := Algorithm(strings.ToLower(algorithm))
	
	var h hash.Hash
	switch alg {
	case SHA256:
		h = sha256.New()
	case MD5:
		h = md5.New()
	case SHA1:
		h = sha1.New()
	default:
		return nil, fmt.Errorf("unsupported checksum algorithm: %s (supported: sha256, md5, sha1)", algorithm)
	}
	
	return &Hasher{
		Hash:      h,
		Algorithm: alg,
	}, nil
}

// CalculateFile calculates the checksum of a file
func CalculateFile(filePath string, algorithm string) (string, error) {
	hasher, err := NewHasher(algorithm)
	if err != nil {
		return "", err
	}
	
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file for checksum: %w", err)
	}
	defer file.Close()
	
	if _, err := io.Copy(hasher, file); err != nil {
		return "", fmt.Errorf("failed to calculate checksum: %w", err)
	}
	
	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}

// CalculateReader calculates the checksum of data from a reader
func CalculateReader(reader io.Reader, algorithm string) (string, error) {
	hasher, err := NewHasher(algorithm)
	if err != nil {
		return "", err
	}
	
	if _, err := io.Copy(hasher, reader); err != nil {
		return "", fmt.Errorf("failed to calculate checksum: %w", err)
	}
	
	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}

// ValidateAlgorithm checks if the algorithm is supported
func ValidateAlgorithm(algorithm string) error {
	_, err := NewHasher(algorithm)
	return err
}

// GetSupportedAlgorithms returns a list of supported algorithms
func GetSupportedAlgorithms() []string {
	return []string{string(SHA256), string(MD5), string(SHA1)}
}
