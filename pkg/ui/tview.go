package ui

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/device"
	"bella/pkg/progress"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// AppTheme defines the complete color scheme for the application
type AppTheme struct {
	// Background colors
	PrimaryBg   tcell.Color
	SecondaryBg tcell.Color
	ModalBg     tcell.Color

	// Text colors
	PrimaryText   tcell.Color
	SecondaryText tcell.Color
	AccentText    tcell.Color

	// Border colors
	BorderColor        tcell.Color
	FocusedBorderColor tcell.Color

	// Status colors
	SuccessColor tcell.Color
	ErrorColor   tcell.Color
	WarningColor tcell.Color
	InfoColor    tcell.Color

	// Progress colors
	ProgressFill  tcell.Color
	ProgressEmpty tcell.Color
	ProgressText  tcell.Color

	// Special UI colors
	TitleColor    tcell.Color
	ButtonColor   tcell.Color
	SelectedColor tcell.Color
}

// DefaultTheme returns the default dark teal-blue theme
func DefaultTheme() *AppTheme {
	return &AppTheme{
		// Dark teal-blue background scheme
		PrimaryBg:   tcell.NewRGBColor(15, 25, 35), // Very dark teal-blue
		SecondaryBg: tcell.NewRGBColor(25, 35, 45), // Slightly lighter teal-blue
		ModalBg:     tcell.NewRGBColor(35, 45, 55), // Modal background

		// Text colors for good contrast
		PrimaryText:   tcell.ColorWhite,
		SecondaryText: tcell.NewRGBColor(180, 190, 200), // Light gray
		AccentText:    tcell.NewRGBColor(100, 200, 255), // Light blue accent

		// Border colors
		BorderColor:        tcell.NewRGBColor(70, 80, 90),    // Subtle border
		FocusedBorderColor: tcell.NewRGBColor(100, 200, 255), // Bright blue when focused

		// Status colors
		SuccessColor: tcell.NewRGBColor(50, 200, 50),   // Green
		ErrorColor:   tcell.NewRGBColor(255, 80, 80),   // Red
		WarningColor: tcell.NewRGBColor(255, 200, 50),  // Yellow
		InfoColor:    tcell.NewRGBColor(100, 200, 255), // Light blue

		// Progress colors
		ProgressFill:  tcell.NewRGBColor(50, 150, 200), // Teal-blue progress
		ProgressEmpty: tcell.NewRGBColor(60, 70, 80),   // Dark empty progress
		ProgressText:  tcell.ColorWhite,

		// Special UI colors
		TitleColor:    tcell.NewRGBColor(100, 200, 255), // Light blue for titles
		ButtonColor:   tcell.NewRGBColor(70, 130, 180),  // Steel blue for buttons
		SelectedColor: tcell.NewRGBColor(50, 100, 150),  // Selection highlight
	}
}

// ApplyTheme applies the theme globally to tview
func (theme *AppTheme) ApplyTheme() {
	// Set global tview styles
	tview.Styles = tview.Theme{
		PrimitiveBackgroundColor:    theme.PrimaryBg,
		ContrastBackgroundColor:     theme.SecondaryBg,
		MoreContrastBackgroundColor: theme.ModalBg,
		BorderColor:                 theme.BorderColor,
		TitleColor:                  theme.TitleColor,
		GraphicsColor:               theme.AccentText,
		PrimaryTextColor:            theme.PrimaryText,
		SecondaryTextColor:          theme.SecondaryText,
		TertiaryTextColor:           theme.SecondaryText,
		InverseTextColor:            theme.PrimaryBg,
		ContrastSecondaryTextColor:  theme.SecondaryText,
	}
}

// colorToHex converts a tcell.Color to a hex string for tview markup
func (theme *AppTheme) colorToHex(color tcell.Color) string {
	r, g, b := color.RGB()
	return fmt.Sprintf("#%02x%02x%02x", r, g, b)
}

// titleCase converts a string to title case (simple implementation)
func titleCase(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(s[:1]) + strings.ToLower(s[1:])
}

// App is the main TUI application structure.
type App struct {
	app   *tview.Application
	pages *tview.Pages
	theme *AppTheme
}

// NewApp creates and initializes the TUI application.
func NewApp() *App {
	theme := DefaultTheme()
	theme.ApplyTheme()

	return &App{
		app:   tview.NewApplication(),
		pages: tview.NewPages(),
		theme: theme,
	}
}

// Run starts the application's main loop.
func (a *App) Run() error {
	mainMenu := a.createMainMenu()

	frame := tview.NewFrame(mainMenu).
		SetBorders(0, 0, 1, 1, 0, 0)

	frame.AddText("Bella - Advanced Data Copier", true, tview.AlignCenter, a.theme.TitleColor)

	sudoStatus := fmt.Sprintf("[%s]Running as root.", a.theme.colorToHex(a.theme.SuccessColor))
	if os.Getuid() != 0 {
		sudoStatus = fmt.Sprintf("[%s]Running as user. Restart with 'sudo' for device access.", a.theme.colorToHex(a.theme.WarningColor))
	}
	frame.AddText(sudoStatus, false, tview.AlignLeft, a.theme.PrimaryText)
	frame.AddText(fmt.Sprintf("[%s]Use arrows, Enter to select, Esc to go back", a.theme.colorToHex(a.theme.InfoColor)), false, tview.AlignRight, a.theme.PrimaryText)

	a.pages.AddPage("main", frame, true, true)
	a.app.SetRoot(a.pages, true).SetFocus(mainMenu)

	a.app.EnableMouse(false)

	return a.app.Run()
}

// goBack is a helper to return to the main menu.
func (a *App) goBack(pageToHide string) {
	a.pages.RemovePage(pageToHide)
	a.pages.SwitchToPage("main")
	a.app.Sync()
	a.app.ForceDraw()
}

// switchToPage is a helper to cleanly switch between pages
func (a *App) switchToPage(pageName string) {
	a.pages.SwitchToPage(pageName)
	a.app.ForceDraw()
}

// createMainMenu builds the main navigation list.
func (a *App) createMainMenu() *tview.List {
	return tview.NewList().
		SetWrapAround(false).
		AddItem("Copy", "Copy files, directories, or devices", 'c', a.showCopyForm).
		AddItem("Verify", "Verify files by comparing source and destination", 'v', a.showVerifyForm).
		AddItem("Wipe", "Securely wipe a device or file", 'w', a.showWipeForm).
		AddItem("List Devices", "Show available storage devices (display only)", 'l', a.showDeviceList).
		AddItem("Quit", "Exit Bella", 'q', func() { a.app.Stop() })
}

// showCopyForm displays the form for copy operations.
func (a *App) showCopyForm() {
	pageName := "copyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddInputField("Threads", "1", 5, nil, nil).
		AddInputField("Count", "-1", 10, nil, nil).
		AddInputField("Skip", "0", 10, nil, nil).
		AddInputField("Seek", "0", 10, nil, nil).
		AddDropDown("Compression", []string{"none", "compress", "decompress", "auto"}, 0, nil).
		AddDropDown("Compression Type", []string{"gzip"}, 0, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil).
		AddCheckbox("Sparse Copy", false, nil).
		AddCheckbox("Skip Bad Sectors", false, nil).
		AddCheckbox("Verify After Copy", false, nil).
		AddCheckbox("Append Mode", false, nil).
		AddCheckbox("Disable Kernel Copy Offload", false, nil).
		AddCheckbox("Preserve Attributes", false, nil).
		AddCheckbox("Dry Run", false, nil)

	form.AddButton("Browse Input", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Output", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Copy", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		threads, _ := strconv.Atoi(form.GetFormItem(3).(*tview.InputField).GetText())
		if threads < 1 {
			threads = 1
		}
		cfg.Threads = threads

		count, _ := strconv.Atoi(form.GetFormItem(4).(*tview.InputField).GetText())
		cfg.Count = count
		skip, _ := strconv.ParseInt(form.GetFormItem(5).(*tview.InputField).GetText(), 10, 64)
		cfg.Skip = skip
		seek, _ := strconv.ParseInt(form.GetFormItem(6).(*tview.InputField).GetText(), 10, 64)
		cfg.Seek = seek

		_, cfg.Compression = form.GetFormItem(7).(*tview.DropDown).GetCurrentOption()
		_, cfg.CompressionType = form.GetFormItem(8).(*tview.DropDown).GetCurrentOption()
		_, checksumAlgorithm := form.GetFormItem(9).(*tview.DropDown).GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		cfg.Sparse = form.GetFormItem(10).(*tview.Checkbox).IsChecked()
		cfg.SkipBadSectors = form.GetFormItem(11).(*tview.Checkbox).IsChecked()
		cfg.Verify = form.GetFormItem(12).(*tview.Checkbox).IsChecked()
		cfg.Append = form.GetFormItem(13).(*tview.Checkbox).IsChecked()
		cfg.UseCopyOffload = !form.GetFormItem(14).(*tview.Checkbox).IsChecked()
		cfg.PreserveAttributes = form.GetFormItem(15).(*tview.Checkbox).IsChecked()
		cfg.DryRun = form.GetFormItem(16).(*tview.Checkbox).IsChecked()

		if _, err := os.Stat(cfg.Output); err == nil && !cfg.Append {
			a.showFileExistsDialog(cfg.Output, func(action string) {
				switch action {
				case "overwrite":
					a.runOperation(cfg)
				case "append":
					cfg.Append = true
					a.runOperation(cfg)
				case "cancel":
				}
			})
		} else {
			a.runOperation(cfg)
		}
	})
	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Copy Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showVerifyForm displays the form for verify operations.
func (a *App) showVerifyForm() {
	pageName := "verifyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Source", "", 40, nil, nil).
		AddInputField("Target", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil)

	form.AddButton("Browse Source", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Verify", func() {
		cfg.Operation = copier.OpVerify
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		_, checksumAlgorithm := form.GetFormItem(3).(*tview.DropDown).GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })
	form.SetBorder(true).SetTitle(" Verify Files ")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDeviceSelector displays a list of devices and calls onSelect with the chosen path.
func (a *App) showDeviceSelector(returnToPage string, onSelect func(path string)) {
	pageName := "deviceSelector"
	list := tview.NewList().SetWrapAround(false)
	list.SetBorder(true).SetTitle("Select a Device")

	devices, err := device.DetectDevices()
	if err != nil {
		a.showDetailedError("Device Detection", err)
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "", 0, nil)
	}

	for i, d := range devices {
		devicePath := d.Path
		sizeStr := progress.HumanizeBytes(uint64(d.Size))
		secondary := fmt.Sprintf("Size: %s, Model: %s", sizeStr, d.Model)
		func(path string) {
			list.AddItem(d.Path, secondary, rune('0'+i), func() {
				a.pages.RemovePage(pageName)
				onSelect(path)
			})
		}(devicePath)
	}

	list.AddItem("Cancel", "Return to previous screen", 'c', func() {
		a.pages.RemovePage(pageName)
		a.switchToPage(returnToPage)
	})

	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage(returnToPage)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}

// runOperation launches the progress screen and the backend logic.
func (a *App) runOperation(cfg *copier.Config) {
	progressPageName := "progressModal"

	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Operation in Progress")

	stageText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	progressBar := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	statsText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)

	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	frame := tview.NewFrame(modal).SetBorders(0, 0, 0, 0, 0, 0)

	centeredFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(frame, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true).
		AddItem(nil, 0, 1, false)

	centeredFlex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		return nil
	})

	a.pages.AddPage(progressPageName, centeredFlex, true, true)
	a.app.SetFocus(centeredFlex)

	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	operationResult := make(chan error, 1)

	go func() {
		defer close(progressChan)
		operationResult <- copier.Execute(cfg)
	}()

	go func() {
		var lastSummary string
		for p := range progressChan {
			currentProgress := p
			a.app.QueueUpdateDraw(func() {
				// We now only update if the stage is NOT finished.
				// This prevents the final summary of one stage from being instantly
				// overwritten by the first tick of the next stage.
				if !currentProgress.IsFinished {
					titleColor := a.theme.colorToHex(a.theme.PrimaryText)
					stageColor := a.theme.colorToHex(a.theme.InfoColor)
					speedColor := a.theme.colorToHex(a.theme.AccentText)

					modal.SetTitle(fmt.Sprintf("[%s]Operation in Progress", titleColor))
					stageText.SetText(fmt.Sprintf("[%s]Stage: [%s]%s", stageColor, titleColor, titleCase(currentProgress.Stage)))
					progressBar.SetText(a.createProgressBar(currentProgress.PercentDone))
					stats := fmt.Sprintf(
						"[%s]Speed:[%s] %.2f MB/s\n[%s]ETA:[%s] %s\n[%s]Data:[%s] %s / %s",
						speedColor, titleColor, currentProgress.AvgSpeed,
						speedColor, titleColor, currentProgress.ETA,
						speedColor, titleColor,
						progress.HumanizeBytes(uint64(currentProgress.BytesDone)),
						progress.HumanizeBytes(uint64(currentProgress.BytesTotal)),
					)
					statsText.SetText(stats)
				}
				// Always store the summary if one is provided.
				if currentProgress.Summary != "" {
					lastSummary = currentProgress.Summary
				}
			})
		}

		err := <-operationResult
		a.app.QueueUpdateDraw(func() {
			// THE ULTIMATE FIX for graphical corruption.
			// Remove the old page, force a sync, then show the new one.
			a.pages.RemovePage(progressPageName)
			a.app.Sync()
			a.showResultModal(err, lastSummary)
		})
	}()
}

// showResultModal creates the final result modal, cleanly displaying all stats.
func (a *App) showResultModal(err error, summary string) {
	resultPageName := "resultModal"
	var message string

	errorColor := a.theme.colorToHex(a.theme.ErrorColor)
	successColor := a.theme.colorToHex(a.theme.SuccessColor)
	textColor := a.theme.colorToHex(a.theme.PrimaryText)

	if err != nil {
		message = fmt.Sprintf("[%s]Operation Failed:\n\n[%s]%v", errorColor, textColor, err)
	} else {
		// Display the final summary text from the reporter.
		message = fmt.Sprintf("[%s]✔ All Stages Complete\n\n[%s]%s", successColor, textColor, summary)
	}

	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"OK"}).
		SetBackgroundColor(a.theme.ModalBg).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			if a.pages.HasPage("main") {
				// This handles GUI mode
				a.pages.RemovePage(resultPageName)
				a.pages.SwitchToPage("main")
			} else {
				// This handles CLI mode
				a.app.Stop()
			}
		})

	a.pages.AddPage(resultPageName, modal, true, true)
}

// createProgressBar is a new helper for the UI
func (a *App) createProgressBar(percent float64) string {
	barWidth := 40
	filled := int(percent * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}

	fillColor := a.theme.colorToHex(a.theme.ProgressFill)
	emptyColor := a.theme.colorToHex(a.theme.ProgressEmpty)
	textColor := a.theme.colorToHex(a.theme.ProgressText)

	bar := fmt.Sprintf("[%s]%s[%s]%s", fillColor, strings.Repeat("█", filled), emptyColor, strings.Repeat("░", barWidth-filled))
	return fmt.Sprintf("%s\n[%s]%.1f%%", bar, textColor, percent)
}

// showWipeForm displays the form for wipe operations.
func (a *App) showWipeForm() {
	pageName := "wipeForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Target Device", "", 40, nil, nil).
		AddDropDown("Mode", []string{"zero", "random"}, 1, nil).
		AddInputField("Passes", "1", 3, nil, nil).
		AddInputField("Block Size", "4M", 10, nil, nil)

	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			form.GetFormItem(3).(*tview.InputField).SetText("auto")
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Wipe", func() {
		cfg.Operation = copier.OpWipe
		cfg.Output = form.GetFormItem(0).(*tview.InputField).GetText()
		_, cfg.WipeMode = form.GetFormItem(1).(*tview.DropDown).GetCurrentOption()
		passes, _ := strconv.Atoi(form.GetFormItem(2).(*tview.InputField).GetText())
		if passes < 1 {
			passes = 1
		}
		cfg.WipePasses = passes

		bsText := form.GetFormItem(3).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Output)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		a.showConfirmation(fmt.Sprintf("This will IRREVERSIBLY DESTROY ALL DATA on %s.\nAre you sure?", cfg.Output), func() {
			a.runOperation(cfg)
		})
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Wipe Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDeviceList displays a list of detected storage devices with detailed info on selection.
func (a *App) showDeviceList() {
	pageName := "deviceList"
	list := tview.NewList().SetWrapAround(false)
	list.SetBorder(true).SetTitle("Available Devices - Press Enter for details")

	devices, err := device.DetectDevices()
	if err != nil {
		a.showDetailedError("Device Detection", err)
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "", 0, nil)
	}

	for i, d := range devices {
		deviceInfo := d
		sizeStr := progress.HumanizeBytes(uint64(d.Size))
		secondary := fmt.Sprintf("Size: %s, Model: %s", sizeStr, d.Model)

		list.AddItem(d.Path, secondary, rune('0'+i), func() {
			a.showDeviceDetails(deviceInfo)
		})
	}

	list.AddItem("Back", "Return to main menu", 'b', func() { a.goBack(pageName) })

	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}

// showDeviceDetails displays detailed information about a device and its partitions
func (a *App) showDeviceDetails(deviceInfo device.Info) {
	pageName := "deviceDetails"
	mainFlex := tview.NewFlex().SetDirection(tview.FlexRow)
	mainFlex.SetBorder(true).SetTitle(fmt.Sprintf("Device Details: %s", deviceInfo.Path))

	detailInfo, err := device.GetDetailedDeviceInfo(deviceInfo.Path)
	if err != nil {
		a.showDetailedError("Get Device Details", err)
		return
	}

	deviceInfoText := tview.NewTextView().SetDynamicColors(true)
	deviceInfoText.SetBorder(true).SetTitle("Device Information")

	var infoBuilder strings.Builder
	fmt.Fprintf(&infoBuilder, "[yellow]Device:[white] %s\n", deviceInfo.Path)
	fmt.Fprintf(&infoBuilder, "[yellow]Name:[white] %s\n", deviceInfo.Name)
	fmt.Fprintf(&infoBuilder, "[yellow]Size:[white] %s\n", progress.HumanizeBytes(uint64(deviceInfo.Size)))

	for key, value := range detailInfo {
		if key != "Size" {
			fmt.Fprintf(&infoBuilder, "[yellow]%s:[white] %s\n", key, value)
		}
	}
	deviceInfoText.SetText(infoBuilder.String())

	partitions, err := device.GetPartitions(deviceInfo.Name)
	if err != nil {
		a.showDetailedError("Get Partitions", err)
		return
	}

	partitionsList := tview.NewList().SetWrapAround(false)
	partitionsList.SetBorder(true).SetTitle("Partitions")

	if len(partitions) == 0 {
		partitionsList.AddItem("No partitions found", "", 0, nil)
	} else {
		for i, p := range partitions {
			sizeStr := progress.HumanizeBytes(uint64(p.Size))
			secondary := fmt.Sprintf("Size: %s", sizeStr)
			partitionsList.AddItem(p.Path, secondary, rune('0'+i), nil)
		}
	}

	partitionsList.AddItem("Back", "Return to device list", 'b', func() {
		a.pages.RemovePage(pageName)
		a.switchToPage("deviceList")
	})

	mainFlex.AddItem(deviceInfoText, 0, 1, false).
		AddItem(partitionsList, 0, 1, true)

	mainFlex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage("deviceList")
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, mainFlex, true, true)
}

// Helper functions
func (a *App) showDetailedError(operation string, err error) {
	pageName := "detailedError"
	modal := tview.NewModal().
		SetText(fmt.Sprintf("Error during %s:\n\n%v", operation, err)).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func (a *App) showConfirmation(message string, onConfirm func()) {
	pageName := "confirmModal"
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
			if buttonLabel == "Yes" {
				onConfirm()
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func (a *App) showFileExistsDialog(filePath string, onAction func(string)) {
	pageName := "fileExistsModal"
	message := fmt.Sprintf("File '%s' already exists.\nWhat would you like to do?", filePath)
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Overwrite", "Append", "Cancel"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
			switch buttonLabel {
			case "Overwrite":
				onAction("overwrite")
			case "Append":
				onAction("append")
			case "Cancel":
				onAction("cancel")
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func parseBlockSize(s string) (int, error) {
	s = strings.ToUpper(strings.TrimSpace(s))
	mult := 1
	suffix := ""
	if strings.HasSuffix(s, "K") || strings.HasSuffix(s, "M") || strings.HasSuffix(s, "G") {
		suffix = s[len(s)-1:]
		s = s[:len(s)-1]
	}
	val, err := strconv.Atoi(s)
	if err != nil {
		return 0, err
	}
	switch suffix {
	case "K":
		mult = 1024
	case "M":
		mult = 1024 * 1024
	case "G":
		mult = 1024 * 1024 * 1024
	}
	return val * mult, nil
}

// RunCLIProgress creates a minimal tview-based progress display for CLI mode
func RunCLIProgress(cfg *copier.Config) error {
	cliApp := NewApp()

	// Create a simple progress display for CLI mode
	progressPageName := "cliProgress"

	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Bella - Operation in Progress")

	stageText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	progressBar := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	statsText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)

	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	// Center the modal on screen
	centeredFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(modal, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true).
		AddItem(nil, 0, 1, false)

	cliApp.pages.AddPage(progressPageName, centeredFlex, true, true)
	cliApp.app.SetRoot(cliApp.pages, true).SetFocus(centeredFlex)

	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	operationResult := make(chan error, 1)

	go func() {
		defer close(progressChan)
		operationResult <- copier.Execute(cfg)
	}()

	go func() {
		var lastSummary string
		for p := range progressChan {
			currentProgress := p
			cliApp.app.QueueUpdateDraw(func() {
				if !currentProgress.IsFinished {
					titleColor := cliApp.theme.colorToHex(cliApp.theme.PrimaryText)
					stageColor := cliApp.theme.colorToHex(cliApp.theme.InfoColor)
					speedColor := cliApp.theme.colorToHex(cliApp.theme.AccentText)

					modal.SetTitle(fmt.Sprintf("[%s]Bella - Operation in Progress", titleColor))
					stageText.SetText(fmt.Sprintf("[%s]Stage: [%s]%s", stageColor, titleColor, titleCase(currentProgress.Stage)))
					progressBar.SetText(cliApp.createProgressBar(currentProgress.PercentDone))
					stats := fmt.Sprintf(
						"[%s]Speed:[%s] %.2f MB/s\n[%s]ETA:[%s] %s\n[%s]Data:[%s] %s / %s",
						speedColor, titleColor, currentProgress.AvgSpeed,
						speedColor, titleColor, currentProgress.ETA,
						speedColor, titleColor,
						progress.HumanizeBytes(uint64(currentProgress.BytesDone)),
						progress.HumanizeBytes(uint64(currentProgress.BytesTotal)),
					)
					statsText.SetText(stats)
				}
				if currentProgress.Summary != "" {
					lastSummary = currentProgress.Summary
				}
			})
		}

		err := <-operationResult
		cliApp.app.QueueUpdateDraw(func() {
			cliApp.pages.RemovePage(progressPageName)
			cliApp.app.Sync()
			cliApp.showResultModal(err, lastSummary)
		})
	}()

	return cliApp.app.Run()
}
