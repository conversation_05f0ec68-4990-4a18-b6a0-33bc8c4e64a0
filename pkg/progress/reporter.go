package progress

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// Info holds all the data for a single progress update.
type Info struct {
	BytesDone   int64
	BytesTotal  int64
	AvgSpeed    float64 // MB/s
	ElapsedTime float64 // seconds
	ETA         string
	PercentDone float64
	Stage       string // For multi-stage operations
	Summary     string // For the final report
	IsFinished  bool   // Flag to indicate the final update for a stage
}

// Reporter handles consistent progress reporting.
type Reporter struct {
	startTime  time.Time
	stage      string
	mu         sync.Mutex
	lastUpdate time.Time
	UpdateChan chan<- Info
}

// NewReporter creates a new progress reporter.
func NewReporter(stage string, updateChan chan<- Info) *Reporter {
	r := &Reporter{
		startTime:  time.Now(),
		stage:      stage,
		UpdateChan: updateChan,
		lastUpdate: time.Now(),
	}
	return r
}

// SetStage updates the reporter's stage name for multi-stage operations.
func (r *Reporter) SetStage(stage string) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.stage = stage
}

// Update calculates and reports progress.
func (r *Reporter) Update(bytesDone, bytesTotal int64) {
	r.mu.Lock()
	defer r.mu.Unlock()

	now := time.Now()
	if now.Sub(r.lastUpdate) < 200*time.Millisecond && bytesDone < bytesTotal {
		return
	}
	r.lastUpdate = now

	elapsed := now.Sub(r.startTime).Seconds()
	if elapsed <= 0 {
		elapsed = 1
	}

	avgSpeed := float64(bytesDone) / elapsed / 1024 / 1024
	if avgSpeed < 0 {
		avgSpeed = 0
	}

	var etaStr string
	if avgSpeed > 0 && bytesTotal > 0 && bytesDone < bytesTotal {
		remainingBytes := float64(bytesTotal - bytesDone)
		etaSeconds := remainingBytes / (avgSpeed * 1024 * 1024)
		if etaSeconds > 0 {
			etaStr = formatETA(etaSeconds)
		}
	}

	percentDone := 0.0
	if bytesTotal > 0 {
		percentDone = (float64(bytesDone) / float64(bytesTotal)) * 100.0
	}
	if percentDone > 100.0 {
		percentDone = 100.0
	}
	if bytesDone >= bytesTotal && bytesTotal > 0 {
		percentDone = 100.0
	}

	info := Info{
		BytesDone:   bytesDone,
		BytesTotal:  bytesTotal,
		AvgSpeed:    avgSpeed,
		ElapsedTime: elapsed,
		ETA:         etaStr,
		PercentDone: percentDone,
		Stage:       r.stage,
	}

	if r.UpdateChan != nil {
		r.UpdateChan <- info
	}
	// Note: No longer support direct terminal printing, everything goes through channels
}

// Finish sends the final report and waits for it to be processed in CLI mode.
func (r *Reporter) Finish(bytesDone int64) {
	r.Update(bytesDone, bytesDone)

	r.mu.Lock()
	defer r.mu.Unlock()

	duration := time.Since(r.startTime).Round(time.Second)
	if duration < time.Second {
		duration = time.Second
	}
	avgSpeed := 0.0
	if duration.Seconds() > 0 {
		avgSpeed = float64(bytesDone) / duration.Seconds() / (1024 * 1024)
	}

	displayStage := strings.Title(strings.ToLower(r.stage))
	summary := fmt.Sprintf("%s completed.\n  - Total Data: %s\n  - Duration: %s\n  - Average Speed: %.2f MB/s",
		displayStage, HumanizeBytes(uint64(bytesDone)), duration, avgSpeed)

	info := Info{
		BytesDone:   bytesDone,
		BytesTotal:  bytesDone,
		PercentDone: 100.0,
		AvgSpeed:    avgSpeed,
		ElapsedTime: duration.Seconds(),
		Stage:       r.stage,
		Summary:     summary,
		IsFinished:  true,
	}

	if r.UpdateChan != nil {
		r.UpdateChan <- info
	}
	// Note: No longer support direct terminal printing, everything goes through channels
}

// formatETA formats seconds into a human-readable string.
func formatETA(etaSeconds float64) string {
	if etaSeconds < 1 {
		return ""
	}
	if etaSeconds >= 3600*24*7 {
		return ">7d"
	}

	d := time.Duration(etaSeconds) * time.Second
	d = d.Round(time.Second)
	h := d / time.Hour
	d -= h * time.Hour
	m := d / time.Minute
	d -= m * time.Minute
	s := d / time.Second

	if h >= 24 {
		days := h / 24
		hours := h % 24
		return fmt.Sprintf("%dd %dh", days, hours)
	}
	if h > 0 {
		return fmt.Sprintf("%02d:%02d:%02d", h, m, s)
	}
	return fmt.Sprintf("%02d:%02d", m, s)
}

// HumanizeBytes converts bytes to a human-readable string.
func HumanizeBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}

	div, exp := int64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %ciB", float64(b)/float64(div), "KMGTPE"[exp])
}
